#!/usr/bin/env python3
"""
CodeGeeX OpenAI API 适配器测试脚本
测试所有可用的API接口功能
"""

import json
import time
import requests
from typing import Dict, Any, Optional


# 配置
BASE_URL = "https://narrow-trout-74.deno.dev"
API_KEY = "sk-dummy-964a3f7da2374e478b8a935380e595e8"  # 从运行日志中获取的API密钥
MODELS = ["claude-3-7-sonnet", "claude-sonnet-4"]

# 测试统计
test_results = {"passed": 0, "failed": 0, "total": 0}


def print_header(title: str):
    """打印测试标题"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")


def print_test(test_name: str, success: bool, details: str = ""):
    """打印测试结果"""
    global test_results
    test_results["total"] += 1
    
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} | {test_name}")
    
    if details:
        print(f"     {details}")
    
    if success:
        test_results["passed"] += 1
    else:
        test_results["failed"] += 1


def test_models_no_auth():
    """测试无认证的模型列表接口"""
    print_header("测试模型列表接口（无认证）")
    
    try:
        response = requests.get(f"{BASE_URL}/models", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            models = [model["id"] for model in data.get("data", [])]
            
            if set(models) == set(MODELS):
                print_test("GET /models", True, f"返回模型: {models}")
            else:
                print_test("GET /models", False, f"模型不匹配，期望: {MODELS}, 实际: {models}")
        else:
            print_test("GET /models", False, f"HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print_test("GET /models", False, f"请求异常: {str(e)}")


def test_models_with_auth():
    """测试需要认证的模型列表接口"""
    print_header("测试模型列表接口（需要认证）")
    
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        response = requests.get(f"{BASE_URL}/v1/models", headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            models = [model["id"] for model in data.get("data", [])]
            
            if set(models) == set(MODELS):
                print_test("GET /v1/models (认证)", True, f"返回模型: {models}")
            else:
                print_test("GET /v1/models (认证)", False, f"模型不匹配，期望: {MODELS}, 实际: {models}")
        else:
            print_test("GET /v1/models (认证)", False, f"HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print_test("GET /v1/models (认证)", False, f"请求异常: {str(e)}")


def test_chat_completion_non_stream():
    """测试非流式聊天完成"""
    print_header("测试聊天完成接口（非流式）")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": MODELS[0],
        "messages": [
            {"role": "user", "content": "你好，请简单介绍一下你自己。"}
        ],
        "stream": False,
        "max_tokens": 100
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{BASE_URL}/v1/chat/completions", 
                               headers=headers, 
                               json=payload, 
                               timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            content = data.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            if content:
                print_test("POST /v1/chat/completions (非流式)", True, 
                          f"响应时间: {end_time-start_time:.2f}s, 内容长度: {len(content)}")
                print(f"     回复预览: {content[:100]}...")
            else:
                print_test("POST /v1/chat/completions (非流式)", False, "响应内容为空")
        else:
            print_test("POST /v1/chat/completions (非流式)", False, 
                      f"HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print_test("POST /v1/chat/completions (非流式)", False, f"请求异常: {str(e)}")


def test_chat_completion_stream():
    """测试流式聊天完成"""
    print_header("测试聊天完成接口（流式）")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": MODELS[0],
        "messages": [
            {"role": "user", "content": "请用一句话介绍Python编程语言。"}
        ],
        "stream": True,
        "max_tokens": 50
    }
    
    try:
        start_time = time.time()
        response = requests.post(f"{BASE_URL}/v1/chat/completions", 
                               headers=headers, 
                               json=payload, 
                               stream=True, 
                               timeout=30)
        
        if response.status_code == 200:
            content_chunks = []
            chunk_count = 0
            
            for line in response.iter_lines():
                if line:
                    line_text = line.decode('utf-8')
                    if line_text.startswith('data: '):
                        data_text = line_text[6:]
                        if data_text == '[DONE]':
                            break
                        try:
                            chunk_data = json.loads(data_text)
                            delta = chunk_data.get("choices", [{}])[0].get("delta", {})
                            if "content" in delta:
                                content_chunks.append(delta["content"])
                                chunk_count += 1
                        except json.JSONDecodeError:
                            continue
            
            end_time = time.time()
            full_content = "".join(content_chunks)
            
            if full_content:
                print_test("POST /v1/chat/completions (流式)", True, 
                          f"响应时间: {end_time-start_time:.2f}s, 块数: {chunk_count}, 内容长度: {len(full_content)}")
                print(f"     回复内容: {full_content}")
            else:
                print_test("POST /v1/chat/completions (流式)", False, "未收到有效内容")
        else:
            print_test("POST /v1/chat/completions (流式)", False, 
                      f"HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print_test("POST /v1/chat/completions (流式)", False, f"请求异常: {str(e)}")


def test_invalid_api_key():
    """测试无效API密钥"""
    print_header("测试错误处理（无效API密钥）")
    
    headers = {"Authorization": "Bearer invalid-key"}
    
    try:
        response = requests.get(f"{BASE_URL}/v1/models", headers=headers, timeout=10)
        
        if response.status_code == 403:
            print_test("无效API密钥测试", True, "正确返回403 Forbidden")
        else:
            print_test("无效API密钥测试", False, f"期望403，实际: {response.status_code}")
            
    except Exception as e:
        print_test("无效API密钥测试", False, f"请求异常: {str(e)}")


def test_invalid_model():
    """测试无效模型"""
    print_header("测试错误处理（无效模型）")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "invalid-model",
        "messages": [{"role": "user", "content": "测试"}],
        "stream": False
    }
    
    try:
        response = requests.post(f"{BASE_URL}/v1/chat/completions", 
                               headers=headers, 
                               json=payload, 
                               timeout=10)
        
        if response.status_code == 404:
            print_test("无效模型测试", True, "正确返回404 Not Found")
        else:
            print_test("无效模型测试", False, f"期望404，实际: {response.status_code}")
            
    except Exception as e:
        print_test("无效模型测试", False, f"请求异常: {str(e)}")


def test_debug_toggle():
    """测试调试模式切换"""
    print_header("测试调试模式切换")
    
    try:
        # 启用调试模式
        response = requests.get(f"{BASE_URL}/debug?enable=true", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("debug_mode") is True:
                print_test("启用调试模式", True, "调试模式已启用")
            else:
                print_test("启用调试模式", False, f"调试模式状态: {data.get('debug_mode')}")
        else:
            print_test("启用调试模式", False, f"HTTP {response.status_code}")
        
        # 禁用调试模式
        response = requests.get(f"{BASE_URL}/debug?enable=false", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get("debug_mode") is False:
                print_test("禁用调试模式", True, "调试模式已禁用")
            else:
                print_test("禁用调试模式", False, f"调试模式状态: {data.get('debug_mode')}")
        else:
            print_test("禁用调试模式", False, f"HTTP {response.status_code}")
            
    except Exception as e:
        print_test("调试模式切换", False, f"请求异常: {str(e)}")


def print_summary():
    """打印测试总结"""
    print_header("测试总结")
    print(f"总测试数: {test_results['total']}")
    print(f"通过: {test_results['passed']} ✅")
    print(f"失败: {test_results['failed']} ❌")
    
    if test_results['failed'] == 0:
        print("\n🎉 所有测试通过！")
    else:
        success_rate = (test_results['passed'] / test_results['total']) * 100
        print(f"\n📊 成功率: {success_rate:.1f}%")


def main():
    """主函数"""
    print("🚀 开始测试 CodeGeeX OpenAI API 适配器")
    print(f"📍 基础URL: {BASE_URL}")
    print(f"🔑 API密钥: {API_KEY[:20]}...")
    
    # 运行所有测试
    test_models_no_auth()
    test_models_with_auth()
    test_chat_completion_non_stream()
    test_chat_completion_stream()
    test_invalid_api_key()
    test_invalid_model()
    test_debug_toggle()
    
    # 打印总结
    print_summary()


if __name__ == "__main__":
    main()
