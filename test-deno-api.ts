#!/usr/bin/env -S deno run --allow-net
/**
 * CodeGeeX OpenAI API 适配器测试脚本 - Deno版本
 * 使用方法: deno run --allow-net test-deno-api.ts
 */

// 配置
const BASE_URL = "https://narrow-trout-74.deno.dev/v1";
const API_KEY = "sk-dummy-964a3f7da2374e478b8a935380e595e8";
const MODELS = ["claude-3-7-sonnet", "claude-sonnet-4"];

// 测试统计
const testResults = { passed: 0, failed: 0, total: 0 };

function printHeader(title: string) {
  console.log(`\n${"=".repeat(60)}`);
  console.log(`  ${title}`);
  console.log(`${"=".repeat(60)}`);
}

function printTest(testName: string, success: boolean, details = "") {
  testResults.total++;
  
  const status = success ? "✅ PASS" : "❌ FAIL";
  console.log(`${status} | ${testName}`);
  
  if (details) {
    console.log(`     ${details}`);
  }
  
  if (success) {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
}

async function testModelsNoAuth() {
  printHeader("测试模型列表接口（无认证）");
  
  try {
    const response = await fetch(`${BASE_URL}/models`);
    
    if (response.ok) {
      const data = await response.json();
      const models = data.data?.map((model: any) => model.id) || [];
      
      const expectedSet = new Set(MODELS);
      const actualSet = new Set(models);
      const isEqual = expectedSet.size === actualSet.size && 
                     [...expectedSet].every(x => actualSet.has(x));
      
      if (isEqual) {
        printTest("GET /models", true, `返回模型: ${models.join(", ")}`);
      } else {
        printTest("GET /models", false, `模型不匹配，期望: ${MODELS.join(", ")}, 实际: ${models.join(", ")}`);
      }
    } else {
      printTest("GET /models", false, `HTTP ${response.status}: ${await response.text()}`);
    }
  } catch (error) {
    printTest("GET /models", false, `请求异常: ${error.message}`);
  }
}

async function testModelsWithAuth() {
  printHeader("测试模型列表接口（需要认证）");
  
  try {
    const response = await fetch(`${BASE_URL}/v1/models`, {
      headers: { "Authorization": `Bearer ${API_KEY}` }
    });
    
    if (response.ok) {
      const data = await response.json();
      const models = data.data?.map((model: any) => model.id) || [];
      
      const expectedSet = new Set(MODELS);
      const actualSet = new Set(models);
      const isEqual = expectedSet.size === actualSet.size && 
                     [...expectedSet].every(x => actualSet.has(x));
      
      if (isEqual) {
        printTest("GET /v1/models (认证)", true, `返回模型: ${models.join(", ")}`);
      } else {
        printTest("GET /v1/models (认证)", false, `模型不匹配，期望: ${MODELS.join(", ")}, 实际: ${models.join(", ")}`);
      }
    } else {
      printTest("GET /v1/models (认证)", false, `HTTP ${response.status}: ${await response.text()}`);
    }
  } catch (error) {
    printTest("GET /v1/models (认证)", false, `请求异常: ${error.message}`);
  }
}

async function testChatCompletionNonStream() {
  printHeader("测试聊天完成接口（非流式）");
  
  const payload = {
    model: MODELS[0],
    messages: [
      { role: "user", content: "你好，请简单介绍一下你自己。" }
    ],
    stream: false,
    max_tokens: 100
  };
  
  try {
    const startTime = Date.now();
    const response = await fetch(`${BASE_URL}/v1/chat/completions`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${API_KEY}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(payload)
    });
    const endTime = Date.now();
    
    if (response.ok) {
      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || "";
      
      if (content) {
        printTest("POST /v1/chat/completions (非流式)", true, 
                 `响应时间: ${((endTime - startTime) / 1000).toFixed(2)}s, 内容长度: ${content.length}`);
        console.log(`     回复预览: ${content.substring(0, 100)}...`);
      } else {
        printTest("POST /v1/chat/completions (非流式)", false, "响应内容为空");
      }
    } else {
      printTest("POST /v1/chat/completions (非流式)", false, 
               `HTTP ${response.status}: ${await response.text()}`);
    }
  } catch (error) {
    printTest("POST /v1/chat/completions (非流式)", false, `请求异常: ${error.message}`);
  }
}

async function testChatCompletionStream() {
  printHeader("测试聊天完成接口（流式）");
  
  const payload = {
    model: MODELS[0],
    messages: [
      { role: "user", content: "请用一句话介绍Python编程语言。" }
    ],
    stream: true,
    max_tokens: 50
  };
  
  try {
    const startTime = Date.now();
    const response = await fetch(`${BASE_URL}/v1/chat/completions`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${API_KEY}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(payload)
    });
    
    if (response.ok && response.body) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      const contentChunks: string[] = [];
      let chunkCount = 0;
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const dataText = line.substring(6);
              if (dataText === '[DONE]') {
                break;
              }
              try {
                const chunkData = JSON.parse(dataText);
                const delta = chunkData.choices?.[0]?.delta;
                if (delta?.content) {
                  contentChunks.push(delta.content);
                  chunkCount++;
                }
              } catch {
                // 忽略JSON解析错误
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
      
      const endTime = Date.now();
      const fullContent = contentChunks.join("");
      
      if (fullContent) {
        printTest("POST /v1/chat/completions (流式)", true, 
                 `响应时间: ${((endTime - startTime) / 1000).toFixed(2)}s, 块数: ${chunkCount}, 内容长度: ${fullContent.length}`);
        console.log(`     回复内容: ${fullContent}`);
      } else {
        printTest("POST /v1/chat/completions (流式)", false, "未收到有效内容");
      }
    } else {
      printTest("POST /v1/chat/completions (流式)", false, 
               `HTTP ${response.status}: ${await response.text()}`);
    }
  } catch (error) {
    printTest("POST /v1/chat/completions (流式)", false, `请求异常: ${error.message}`);
  }
}

async function testInvalidApiKey() {
  printHeader("测试错误处理（无效API密钥）");
  
  try {
    const response = await fetch(`${BASE_URL}/v1/models`, {
      headers: { "Authorization": "Bearer invalid-key" }
    });
    
    if (response.status === 403) {
      printTest("无效API密钥测试", true, "正确返回403 Forbidden");
    } else {
      printTest("无效API密钥测试", false, `期望403，实际: ${response.status}`);
    }
  } catch (error) {
    printTest("无效API密钥测试", false, `请求异常: ${error.message}`);
  }
}

async function testInvalidModel() {
  printHeader("测试错误处理（无效模型）");
  
  const payload = {
    model: "invalid-model",
    messages: [{ role: "user", content: "测试" }],
    stream: false
  };
  
  try {
    const response = await fetch(`${BASE_URL}/v1/chat/completions`, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${API_KEY}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(payload)
    });
    
    if (response.status === 404) {
      printTest("无效模型测试", true, "正确返回404 Not Found");
    } else {
      printTest("无效模型测试", false, `期望404，实际: ${response.status}`);
    }
  } catch (error) {
    printTest("无效模型测试", false, `请求异常: ${error.message}`);
  }
}

async function testDebugToggle() {
  printHeader("测试调试模式切换");
  
  try {
    // 启用调试模式
    const enableResponse = await fetch(`${BASE_URL}/debug?enable=true`);
    
    if (enableResponse.ok) {
      const data = await enableResponse.json();
      if (data.debug_mode === true) {
        printTest("启用调试模式", true, "调试模式已启用");
      } else {
        printTest("启用调试模式", false, `调试模式状态: ${data.debug_mode}`);
      }
    } else {
      printTest("启用调试模式", false, `HTTP ${enableResponse.status}`);
    }
    
    // 禁用调试模式
    const disableResponse = await fetch(`${BASE_URL}/debug?enable=false`);
    
    if (disableResponse.ok) {
      const data = await disableResponse.json();
      if (data.debug_mode === false) {
        printTest("禁用调试模式", true, "调试模式已禁用");
      } else {
        printTest("禁用调试模式", false, `调试模式状态: ${data.debug_mode}`);
      }
    } else {
      printTest("禁用调试模式", false, `HTTP ${disableResponse.status}`);
    }
  } catch (error) {
    printTest("调试模式切换", false, `请求异常: ${error.message}`);
  }
}

function printSummary() {
  printHeader("测试总结");
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过: ${testResults.passed} ✅`);
  console.log(`失败: ${testResults.failed} ❌`);
  
  if (testResults.failed === 0) {
    console.log("\n🎉 所有测试通过！");
  } else {
    const successRate = (testResults.passed / testResults.total) * 100;
    console.log(`\n📊 成功率: ${successRate.toFixed(1)}%`);
  }
}

async function main() {
  console.log("🚀 开始测试 CodeGeeX OpenAI API 适配器 (Deno版本)");
  console.log(`📍 基础URL: ${BASE_URL}`);
  console.log(`🔑 API密钥: ${API_KEY.substring(0, 20)}...`);
  
  // 运行所有测试
  await testModelsNoAuth();
  await testModelsWithAuth();
  await testChatCompletionNonStream();
  await testChatCompletionStream();
  await testInvalidApiKey();
  await testInvalidModel();
  await testDebugToggle();
  
  // 打印总结
  printSummary();
}

if (import.meta.main) {
  await main();
}
