/**
 * CodeGeeX OpenAI API 适配器 - Deno版本
 * 可直接部署到 Deno Deploy 或在 Deno Playground 中运行
 */

// 配置常量
const CODEGEEX_MODELS = ["claude-3-7-sonnet", "claude-sonnet-4"];
const MAX_ERROR_COUNT = 3;
const ERROR_COOLDOWN = 300000; // 5分钟

// 从环境变量获取配置，如果没有则使用默认值
const CLIENT_API_KEYS = (Deno.env.get("CLIENT_API_KEYS") || "sk-dummy-964a3f7da2374e478b8a935380e595e8").split(",");
const CODEGEEX_TOKENS = (Deno.env.get("CODEGEEX_TOKENS") || "b4728d3d-62be-46ef-b2f0-7b341b21de28").split(",");

// 全局状态
let DEBUG_MODE = Deno.env.get("DEBUG_MODE") === "true";
const tokenStatus = new Map<string, {
  isValid: boolean;
  lastUsed: number;
  errorCount: number;
}>();

// 初始化令牌状态
CODEGEEX_TOKENS.forEach(token => {
  tokenStatus.set(token, {
    isValid: true,
    lastUsed: 0,
    errorCount: 0
  });
});

// 工具函数
function logDebug(message: string) {
  if (DEBUG_MODE) {
    console.log(`[DEBUG] ${message}`);
  }
}

function generateId(): string {
  return crypto.randomUUID().replace(/-/g, "");
}

function getBestToken(): string | null {
  const now = Date.now();
  const validTokens = Array.from(tokenStatus.entries())
    .filter(([_, status]) => 
      status.isValid && (
        status.errorCount < MAX_ERROR_COUNT || 
        now - status.lastUsed > ERROR_COOLDOWN
      )
    );

  if (validTokens.length === 0) return null;

  // 重置冷却期过后的令牌错误计数
  validTokens.forEach(([token, status]) => {
    if (status.errorCount >= MAX_ERROR_COUNT && now - status.lastUsed > ERROR_COOLDOWN) {
      status.errorCount = 0;
    }
  });

  // 按最后使用时间和错误计数排序
  validTokens.sort(([, a], [, b]) => a.lastUsed - b.lastUsed || a.errorCount - b.errorCount);
  
  const [selectedToken, status] = validTokens[0];
  status.lastUsed = now;
  return selectedToken;
}

// 认证中间件
function authenticate(request: Request): boolean {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return CLIENT_API_KEYS.includes(token);
}

// 消息格式转换
function convertMessagesToCodeGeeXFormat(messages: any[]): { prompt: string; history: any[] } {
  if (!messages || messages.length === 0) {
    throw new Error("No messages provided");
  }

  // 找到最后一个用户消息作为prompt
  let lastUserMsg = null;
  for (let i = messages.length - 1; i >= 0; i--) {
    if (messages[i].role === "user") {
      lastUserMsg = messages[i];
      break;
    }
  }

  if (!lastUserMsg) {
    throw new Error("No user message found");
  }

  const prompt = typeof lastUserMsg.content === "string" ? lastUserMsg.content : "";
  
  // 构建历史记录
  const history: any[] = [];
  let userContent = "";
  let assistantContent = "";

  for (const msg of messages) {
    if (msg === lastUserMsg) continue;

    if (msg.role === "user") {
      if (userContent && assistantContent) {
        history.push({
          query: userContent,
          answer: assistantContent,
          id: generateId()
        });
        userContent = "";
        assistantContent = "";
      }
      userContent = typeof msg.content === "string" ? msg.content : "";
    } else if (msg.role === "assistant") {
      assistantContent = typeof msg.content === "string" ? msg.content : "";
      if (userContent) {
        history.push({
          query: userContent,
          answer: assistantContent,
          id: generateId()
        });
        userContent = "";
        assistantContent = "";
      }
    }
  }

  return { prompt, history };
}

// 流式响应生成器
async function* generateStreamResponse(response: Response, model: string) {
  const streamId = `chatcmpl-${generateId()}`;
  const createdTime = Math.floor(Date.now() / 1000);

  // 发送初始角色增量
  yield `data: ${JSON.stringify({
    id: streamId,
    object: "chat.completion.chunk",
    created: createdTime,
    model,
    choices: [{ delta: { role: "assistant" }, index: 0 }]
  })}\n\n`;

  if (!response.body) return;

  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let buffer = "";

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });

      // 处理完整的事件块
      while (buffer.includes("\n\n")) {
        const [eventData, ...rest] = buffer.split("\n\n");
        buffer = rest.join("\n\n");

        if (!eventData.trim()) continue;

        let eventType = "";
        let dataJson: any = null;

        for (const line of eventData.split("\n")) {
          const trimmed = line.trim();
          if (trimmed.startsWith("event:")) {
            eventType = trimmed.substring(6).trim();
          } else if (trimmed.startsWith("data:")) {
            try {
              dataJson = JSON.parse(trimmed.substring(5).trim());
            } catch {
              continue;
            }
          }
        }

        if (eventType === "add" && dataJson?.text) {
          yield `data: ${JSON.stringify({
            id: streamId,
            object: "chat.completion.chunk",
            created: createdTime,
            model,
            choices: [{ delta: { content: dataJson.text }, index: 0 }]
          })}\n\n`;
        } else if (eventType === "finish") {
          yield `data: ${JSON.stringify({
            id: streamId,
            object: "chat.completion.chunk",
            created: createdTime,
            model,
            choices: [{ delta: {}, index: 0, finish_reason: "stop" }]
          })}\n\n`;
          yield "data: [DONE]\n\n";
          return;
        }
      }
    }
  } catch (error) {
    logDebug(`Stream processing error: ${error}`);
  } finally {
    reader.releaseLock();
  }

  // 如果流意外结束
  yield `data: ${JSON.stringify({
    id: streamId,
    object: "chat.completion.chunk",
    created: createdTime,
    model,
    choices: [{ delta: {}, index: 0, finish_reason: "stop" }]
  })}\n\n`;
  yield "data: [DONE]\n\n";
}

// 非流式响应处理
async function buildNonStreamResponse(response: Response, model: string) {
  let fullContent = "";
  
  if (!response.body) {
    throw new Error("No response body");
  }

  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  let buffer = "";

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });

      while (buffer.includes("\n\n")) {
        const [eventData, ...rest] = buffer.split("\n\n");
        buffer = rest.join("\n\n");

        if (!eventData.trim()) continue;

        let eventType = "";
        let dataJson: any = null;

        for (const line of eventData.split("\n")) {
          const trimmed = line.trim();
          if (trimmed.startsWith("event:")) {
            eventType = trimmed.substring(6).trim();
          } else if (trimmed.startsWith("data:")) {
            try {
              dataJson = JSON.parse(trimmed.substring(5).trim());
            } catch {
              continue;
            }
          }
        }

        if (eventType === "add" && dataJson?.text) {
          fullContent += dataJson.text;
        } else if (eventType === "finish") {
          if (dataJson?.text) {
            fullContent = dataJson.text;
          }
          return {
            id: `chatcmpl-${generateId()}`,
            object: "chat.completion",
            created: Math.floor(Date.now() / 1000),
            model,
            choices: [{
              message: { role: "assistant", content: fullContent },
              index: 0,
              finish_reason: "stop"
            }],
            usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
          };
        }
      }
    }
  } finally {
    reader.releaseLock();
  }

  return {
    id: `chatcmpl-${generateId()}`,
    object: "chat.completion",
    created: Math.floor(Date.now() / 1000),
    model,
    choices: [{
      message: { role: "assistant", content: fullContent },
      index: 0,
      finish_reason: "stop"
    }],
    usage: { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 }
  };
}

// HTML 主页模板
function getHomePage(): string {
  return `<!DOCTYPE html>
<html lang="zh-CN" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeGeeX OpenAI API 适配器</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="h-full bg-gradient-to-br from-blue-50 to-indigo-100">
    <div class="min-h-full">
        <!-- 头部 -->
        <header class="bg-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <h1 class="text-3xl font-bold text-gray-900">
                            🚀 CodeGeeX OpenAI API 适配器
                        </h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-2"></div>
                            <span class="text-sm font-medium text-gray-700">服务运行中</span>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- 状态监控卡片 -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">📊 接口状态监控</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="border rounded-lg p-4 bg-green-50 border-green-200">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium text-gray-900">模型列表</h3>
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                        <div class="text-sm space-y-1">
                            <div class="text-green-700">状态: 正常</div>
                            <div class="text-gray-600">GET /models</div>
                        </div>
                    </div>
                    <div class="border rounded-lg p-4 bg-green-50 border-green-200">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium text-gray-900">认证模型列表</h3>
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                        <div class="text-sm space-y-1">
                            <div class="text-green-700">状态: 正常</div>
                            <div class="text-gray-600">GET /v1/models</div>
                        </div>
                    </div>
                    <div class="border rounded-lg p-4 bg-green-50 border-green-200">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium text-gray-900">聊天完成</h3>
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                        <div class="text-sm space-y-1">
                            <div class="text-green-700">状态: 正常</div>
                            <div class="text-gray-600">POST /v1/chat/completions</div>
                        </div>
                    </div>
                    <div class="border rounded-lg p-4 bg-green-50 border-green-200">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium text-gray-900">调试模式</h3>
                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                        <div class="text-sm space-y-1">
                            <div class="text-green-700">状态: 正常</div>
                            <div class="text-gray-600">GET /debug</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API 文档 -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">📚 API 文档</h2>
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="space-y-6">
                        <!-- 模型列表接口 -->
                        <div class="border-b pb-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">获取模型列表</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <code class="text-sm text-gray-800">
                                    GET /models<br>
                                    GET /v1/models (需要认证)
                                </code>
                            </div>
                        </div>

                        <!-- 聊天完成接口 -->
                        <div class="border-b pb-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">聊天完成</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <code class="text-sm text-gray-800">
                                    POST /v1/chat/completions (需要认证)
                                </code>
                            </div>
                        </div>

                        <!-- 调试接口 -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">调试模式</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <code class="text-sm text-gray-800">
                                    GET /debug?enable=true/false
                                </code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用示例 -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">💡 使用示例</h2>
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900">cURL 示例</h3>
                        <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                            <pre class="text-green-400 text-sm"><code>curl -X POST "https://your-deployment.deno.dev/v1/chat/completions" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "model": "claude-3-7-sonnet",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'</code></pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置信息 -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">⚙️ 配置信息</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">支持的模型</h3>
                        <ul class="space-y-2">
                            <li class="flex items-center text-gray-700">
                                <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                                claude-3-7-sonnet
                            </li>
                            <li class="flex items-center text-gray-700">
                                <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                                claude-sonnet-4
                            </li>
                        </ul>
                    </div>
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">环境变量</h3>
                        <ul class="space-y-2 text-sm">
                            <li class="text-gray-700">
                                <code class="bg-gray-100 px-2 py-1 rounded">CLIENT_API_KEYS</code>
                                - 客户端API密钥
                            </li>
                            <li class="text-gray-700">
                                <code class="bg-gray-100 px-2 py-1 rounded">CODEGEEX_TOKENS</code>
                                - CodeGeeX令牌
                            </li>
                            <li class="text-gray-700">
                                <code class="bg-gray-100 px-2 py-1 rounded">DEBUG_MODE</code>
                                - 调试模式开关
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="bg-white border-t">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <p class="text-center text-gray-500 text-sm">
                    CodeGeeX OpenAI API 适配器 - Deno 版本 |
                    <a href="https://github.com" class="text-blue-600 hover:underline">GitHub</a>
                </p>
            </div>
        </footer>
    </div>
</body>
</html>`;
}

// 主要的请求处理函数
async function handleRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const path = url.pathname;
  const method = request.method;

  logDebug(`${method} ${path}`);

  // CORS 处理
  if (method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };

  try {
    // 路由处理
    if (method === "GET" && path === "/") {
      // 主页
      return new Response(getHomePage(), {
        headers: {
          ...corsHeaders,
          "Content-Type": "text/html; charset=utf-8",
        },
      });

    } else if (method === "GET" && path === "/health") {
      // 健康检查端点
      return Response.json({
        status: "healthy",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
        endpoints: {
          models: "operational",
          chat: "operational",
          debug: "operational"
        }
      }, { headers: corsHeaders });

    } else if (method === "GET" && path === "/models") {
      // 无认证的模型列表
      return Response.json({
        object: "list",
        data: CODEGEEX_MODELS.map(model => ({
          id: model,
          object: "model",
          created: Math.floor(Date.now() / 1000),
          owned_by: "anthropic"
        }))
      }, { headers: corsHeaders });

    } else if (method === "GET" && path === "/v1/models") {
      // 需要认证的模型列表
      if (!authenticate(request)) {
        return Response.json(
          { error: { message: "Invalid API key", type: "authentication_error" } },
          { status: 403, headers: corsHeaders }
        );
      }

      return Response.json({
        object: "list",
        data: CODEGEEX_MODELS.map(model => ({
          id: model,
          object: "model",
          created: Math.floor(Date.now() / 1000),
          owned_by: "anthropic"
        }))
      }, { headers: corsHeaders });

    } else if (method === "GET" && path === "/debug") {
      // 调试模式切换
      const enable = url.searchParams.get("enable");
      if (enable !== null) {
        DEBUG_MODE = enable === "true";
      }
      return Response.json({ debug_mode: DEBUG_MODE }, { headers: corsHeaders });

    } else if (method === "POST" && path === "/v1/chat/completions") {
      // 聊天完成接口
      if (!authenticate(request)) {
        return Response.json(
          { error: { message: "Invalid API key", type: "authentication_error" } },
          { status: 403, headers: corsHeaders }
        );
      }

      const body = await request.json();
      
      if (!CODEGEEX_MODELS.includes(body.model)) {
        return Response.json(
          { error: { message: `Model '${body.model}' not found`, type: "invalid_request_error" } },
          { status: 404, headers: corsHeaders }
        );
      }

      if (!body.messages || body.messages.length === 0) {
        return Response.json(
          { error: { message: "No messages provided", type: "invalid_request_error" } },
          { status: 400, headers: corsHeaders }
        );
      }

      // 转换消息格式
      const { prompt, history } = convertMessagesToCodeGeeXFormat(body.messages);

      // 尝试获取可用令牌
      const token = getBestToken();
      if (!token) {
        return Response.json(
          { error: { message: "No valid CodeGeeX tokens available", type: "service_unavailable" } },
          { status: 503, headers: corsHeaders }
        );
      }

      // 构建请求
      const payload = {
        user_role: 0,
        ide: "VSCode",
        ide_version: "",
        plugin_version: "",
        prompt,
        machineId: "",
        talkId: generateId(),
        locale: "",
        model: body.model,
        agent: null,
        candidates: {
          candidate_msg_id: "",
          candidate_type: "",
          selected_candidate: "",
        },
        history,
      };

      try {
        const codegeexResponse = await fetch("https://codegeex.cn/prod/code/chatCodeSseV3/chat", {
          method: "POST",
          headers: {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "text/event-stream",
            "Content-Type": "application/json",
            "code-token": token,
          },
          body: JSON.stringify(payload),
        });

        if (!codegeexResponse.ok) {
          const status = tokenStatus.get(token)!;
          if (codegeexResponse.status === 401 || codegeexResponse.status === 403) {
            status.isValid = false;
          } else {
            status.errorCount++;
          }
          
          throw new Error(`CodeGeeX API error: ${codegeexResponse.status}`);
        }

        if (body.stream) {
          // 流式响应
          const stream = new ReadableStream({
            async start(controller) {
              try {
                for await (const chunk of generateStreamResponse(codegeexResponse, body.model)) {
                  controller.enqueue(new TextEncoder().encode(chunk));
                }
              } catch (error) {
                logDebug(`Stream error: ${error}`);
              } finally {
                controller.close();
              }
            },
          });

          return new Response(stream, {
            headers: {
              ...corsHeaders,
              "Content-Type": "text/event-stream",
              "Cache-Control": "no-cache",
              "Connection": "keep-alive",
            },
          });
        } else {
          // 非流式响应
          const result = await buildNonStreamResponse(codegeexResponse, body.model);
          return Response.json(result, { headers: corsHeaders });
        }

      } catch (error) {
        logDebug(`Request error: ${error}`);
        return Response.json(
          { error: { message: "Internal server error", type: "server_error" } },
          { status: 500, headers: corsHeaders }
        );
      }

    } else {
      // 404 处理
      return Response.json(
        { error: { message: "Not found", type: "not_found" } },
        { status: 404, headers: corsHeaders }
      );
    }

  } catch (error) {
    logDebug(`Handler error: ${error}`);
    return Response.json(
      { error: { message: "Internal server error", type: "server_error" } },
      { status: 500, headers: corsHeaders }
    );
  }
}

// 启动服务器
console.log("🚀 CodeGeeX OpenAI API Adapter (Deno版本) 启动中...");
console.log(`📊 客户端API密钥数量: ${CLIENT_API_KEYS.length}`);
console.log(`🔑 CodeGeeX令牌数量: ${CODEGEEX_TOKENS.length}`);
console.log(`🎯 支持的模型: ${CODEGEEX_MODELS.join(", ")}`);
console.log(`🐛 调试模式: ${DEBUG_MODE ? "启用" : "禁用"}`);
console.log("📡 服务器已启动，等待请求...");

// Deno Deploy 兼容的服务器启动
Deno.serve({ port: 8001 }, handleRequest);

/*
使用说明：

1. 在 Deno Playground 中运行：
   - 直接复制粘贴此代码到 https://dash.deno.com/playground
   - 点击运行即可

2. 本地运行：
   deno run --allow-net --allow-env codegeex-api.ts

3. 环境变量配置：
   - CLIENT_API_KEYS: 客户端API密钥，多个用逗号分隔
   - CODEGEEX_TOKENS: CodeGeeX令牌，多个用逗号分隔
   - DEBUG_MODE: 设置为 "true" 启用调试模式

4. API端点：
   - GET /models - 获取模型列表（无需认证）
   - GET /v1/models - 获取模型列表（需要认证）
   - POST /v1/chat/completions - 聊天完成（需要认证）
   - GET /debug?enable=true/false - 切换调试模式

5. 示例请求：
   curl -X POST "https://your-deployment.deno.dev/v1/chat/completions" \
     -H "Authorization: Bearer sk-dummy-key" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "claude-3-7-sonnet",
       "messages": [{"role": "user", "content": "Hello!"}],
       "stream": false
     }'
*/
